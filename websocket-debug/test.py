import asyncio
import os
import wave
import datetime
import logging
import io
from fastapi import FastAPI, WebSocket
from fastapi.websockets import WebSocketDisconnect
from dotenv import load_dotenv
from google.cloud import storage

# --- Environment and Logging Configuration ---
# Load environment variables from .env file for local development
load_dotenv()

# Configure logging to show info messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    force=True
)
logger = logging.getLogger(__name__)


# --- Google Cloud Storage Configuration ---
# Get the GCS bucket name from environment variables.
# This is the bucket where your audio files will be saved.
GCS_BUCKET_NAME = os.getenv('GCS_BUCKET_NAME')

# Initialize the GCS client.
# The client will automatically use the service account credentials
# available in the Cloud Run environment.
storage_client = storage.Client()


# --- FastAPI Application Instance ---
app = FastAPI(title="Real-time Audio Processing Service")


# --- Core Functions ---
def save_audio_to_gcs(audio_bytes: bytes, bucket_name: str):
    """
    Saves the provided audio bytes as a WAV file to Google Cloud Storage.

    Args:
        audio_bytes: The raw PCM audio data to save.
        bucket_name: The name of the GCS bucket to upload to.
    """
    if not bucket_name:
        logger.error("GCS_BUCKET_NAME environment variable not set. Skipping save.")
        return

    if not audio_bytes:
        logger.warning("No audio bytes received. Skipping save.")
        return

    try:
        # Create a unique filename for the audio file
        timestamp = datetime.datetime.now(datetime.timezone.utc).strftime("%Y%m%d_%H%M%S")
        destination_blob_name = f"audio-recordings/call_{timestamp}.wav"

        # Get the bucket
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(destination_blob_name)

        # Create an in-memory binary stream
        in_mem_file = io.BytesIO()

        # Write the WAV header and PCM data to the in-memory file
        with wave.open(in_mem_file, 'wb') as wf:
            wf.setnchannels(1)        # Mono
            wf.setsampwidth(2)        # 16-bit PCM
            wf.setframerate(8000)     # 8kHz sample rate
            wf.writeframes(audio_bytes)

        # Rewind the in-memory file to the beginning
        in_mem_file.seek(0)

        # Upload the in-memory file to GCS
        logger.info(f"Uploading {len(audio_bytes)} bytes to gs://{bucket_name}/{destination_blob_name}...")
        blob.upload_from_file(in_mem_file, content_type='audio/wav')

        logger.info(f"Successfully saved audio to gs://{bucket_name}/{destination_blob_name}")

    except Exception as e:
        logger.error(f"Failed to upload to GCS: {e}", exc_info=True)


# --- API Endpoints ---
@app.get("/", summary="Health Check")
async def index_page():
    """Simple health check endpoint."""
    return {"message": "Media Stream Server is running!"}


@app.websocket("/rtp-stream")
async def audio_handler(websocket: WebSocket):
    """
    Handles incoming WebSocket connections, buffers audio data,
    and saves the complete audio to GCS upon disconnection.
    """
    await websocket.accept()
    logger.info(f"Client connected: {websocket.client}")
    
    # Use a bytearray to efficiently buffer incoming audio chunks
    audio_buffer = bytearray()
    
    try:
        while True:
            # Receive audio data from the WebSocket
            message = await websocket.receive_bytes()
            if len(message) < 150:
                logger.info(f"Received {len(message)} bytes")
                continue
            audio_buffer.extend(message)
            logger.info(f"Received {len(message)} bytes, buffer size: {len(audio_buffer)}")

    except WebSocketDisconnect:
        logger.info("Client disconnected. Preparing to save audio.")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
    finally:
        # This block executes when the connection is closed for any reason.
        # Save the buffered audio to a WAV file in GCS.
        save_audio_to_gcs(bytes(audio_buffer), GCS_BUCKET_NAME)
        logger.info("WebSocket handler finished cleanup.")


# --- Main execution (for local testing) ---
if __name__ == "__main__":
    import uvicorn
    # Make sure you have `GCS_BUCKET_NAME` in your .env file for local testing
    # and that you've run `gcloud auth application-default login`.
    if not GCS_BUCKET_NAME:
        logger.warning("GCS_BUCKET_NAME is not set in environment. Audio will not be saved.")
        
    uvicorn.run(
        app,
        host=os.getenv('HOST', '0.0.0.0'),
        port=int(os.getenv('PORT', 8088)),
        log_level="info"
    )

