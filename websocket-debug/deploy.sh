#!/bin/bash

# Configuration
PROJECT_ID="noble-courier-418207"  # Replace with your actual GCP project ID
SERVICE_NAME="rtp-stream"
REGION="us-central1"  # Change if you prefer a different region
IMAGE_NAME="websocket-debug"
PORT=8088

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting deployment to Google Cloud Run...${NC}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Error: gcloud CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}Error: Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Prompt for project ID if not set
if [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    echo -e "${YELLOW}Please enter your GCP Project ID:${NC}"
    read -r PROJECT_ID
fi

# Set the project
echo -e "${YELLOW}Setting GCP project to: $PROJECT_ID${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}Enabling required APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Build the Docker image using Cloud Build
echo -e "${YELLOW}Building Docker image with Cloud Build...${NC}"
gcloud builds submit --tag gcr.io/$PROJECT_ID/$IMAGE_NAME .

if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Docker build failed${NC}"
    exit 1
fi

echo -e "${GREEN}Docker image built successfully!${NC}"

# Deploy to Cloud Run with your preferred configuration
echo -e "${YELLOW}Deploying to Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$IMAGE_NAME \
    --region $REGION \
    --port $PORT \
    --concurrency 80 \
    --cpu-boost \
    --allow-unauthenticated

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Deployment successful!${NC}"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')
    
    echo -e "${GREEN}Service deployed at: $SERVICE_URL${NC}"
    echo -e "${GREEN}WebSocket endpoint: ${SERVICE_URL}/rtp-stream${NC}"
    echo -e "${YELLOW}Note: Replace 'https://' with 'wss://' for WebSocket connections${NC}"
    
    # Convert HTTPS URL to WSS for WebSocket
    WSS_URL=$(echo $SERVICE_URL | sed 's/https:/wss:/')
    echo -e "${GREEN}WebSocket URL: ${WSS_URL}/rtp-stream${NC}"
else
    echo -e "${RED}Deployment failed${NC}"
    exit 1
fi
