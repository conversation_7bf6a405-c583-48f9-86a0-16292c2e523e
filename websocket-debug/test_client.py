import asyncio
import ssl
import websockets
import sys
import os

async def test_websocket(domain):
    """
    Connects to a WebSocket server, sends a sample binary audio chunk, 
    and prints the response.
    """
    if not domain:
        print("Error: Please provide the domain name (e.g., rtp.najoomi.ai).")
        return

    # Construct the secure WebSocket URI
    uri = f"wss://{domain}/rtp-stream"
    print(f"Attempting to connect to: {uri}")

    try:
        # The websockets library handles the SSL/TLS context automatically
        async with websockets.connect(uri) as websocket:
            print("Connection successful!")

            # Create a sample binary payload to simulate a chunk of raw audio
            # For a real client, this would be the actual audio data from Asterisk
            # We'll create 1600 bytes of mock audio data (e.g., 1/10th of a second at 8kHz 16-bit)
            binary_message = os.urandom(1600)
            print(f"Sending binary audio data: {len(binary_message)} bytes")
            await websocket.send(binary_message)

            print("Waiting for response...")
            response = await websocket.recv()
            
            print("---- RESPONSE FROM SERVER ----")
            print(f"'{response}'")
            print("----------------------------")

    except websockets.exceptions.InvalidStatusCode as e:
        print(f"Connection failed: {e.status_code} {e.reason}")
        print("This might happen if the SSL certificate is not active yet or if the backend is down.")
    except ConnectionRefusedError:
        print("Connection refused. This is unusual for an ALB; check if the load balancer is fully provisioned.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_client_alb.py <your.domain.com>")
    else:
        domain_to_test = sys.argv[1]
        asyncio.run(test_websocket(domain_to_test))
