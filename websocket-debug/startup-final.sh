#!/bin/bash
# Install dependencies
apt-get update
apt-get install -y wget socat

# --- Health Check Responder ---
# Run a tiny web server in the background on the health port.
# It responds with a simple "200 OK" to any request.
HEALTH_PORT_METADATA=$(curl http://metadata.google.internal/computeMetadata/v1/instance/attributes/health-port -H "Metadata-Flavor: Google")
(while true; do { echo -e 'HTTP/1.1 200 OK\r\n'; } | socat - TCP-LISTEN:$HEALTH_PORT_METADATA,fork,reuseaddr; done) &

# --- Main Application Proxy ---
# Download websocat binary and make it executable
wget https://github.com/vi/websocat/releases/download/v1.12.0/websocat.x86_64-unknown-linux-musl -O /usr/local/bin/websocat
chmod +x /usr/local/bin/websocat

# Fetch configuration from metadata server
CR_URL=$(curl http://metadata.google.internal/computeMetadata/v1/instance/attributes/cloud-run-url -H "Metadata-Flavor: Google")
APP_PORT_METADATA=$(curl http://metadata.google.internal/computeMetadata/v1/instance/attributes/app-port -H "Metadata-Flavor: Google")

# Get the Cloud Run hostname by stripping the protocol prefix
CR_HOST=${CR_URL#https://}

# Run websocat in the foreground to listen for TCP and connect to the WebSocket endpoint
# This should be the last command in the script.
/usr/local/bin/websocat --binary tcp-l:0.0.0.0:$APP_PORT_METADATA ws-c:wss://$CR_HOST/rtp-stream
