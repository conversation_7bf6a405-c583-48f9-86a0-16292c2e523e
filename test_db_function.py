#!/usr/bin/env python3
"""
Test script for the read_function_schema database function.
This allows us to test the function locally without deploying.
"""

import asyncio
import asyncpg
import os
import json
from dotenv import load_dotenv
from db import db_manager

# Load environment variables
load_dotenv()

async def test_read_function_schema():
    """Test the read_function_schema function with various scenarios."""
    
    print("=== Testing read_function_schema function ===\n")
    
    # Initialize database manager
    db = db_manager()
    
    # Create database connection pool
    try:
        pool = await asyncpg.create_pool(
            user=os.getenv('user'),
            password=os.getenv('password'),
            database=os.getenv('dbname'),
            host=os.getenv('host'),
            port=os.getenv('port'),
            min_size=1,
            max_size=10
        )
        print("✅ Database connection pool created successfully")
    except Exception as e:
        print(f"❌ Failed to create database connection pool: {e}")
        return
    
    
        
    try:
        client_id = 'TCS'
        result = await db.read_function_schema(pool, client_id)
        
        if result == (None, None, None):
            print(f"❌ No data found for client_id: TCS")
        else:
            function_tools, system_message, base_url = result
            # print(f"✅ Success for client_id: '{client_id}'")
            # print(f"   - Function tools count: {len(function_tools) if function_tools else 0}")
            # print(f"   - System message length: {len(system_message) if system_message else 0}")
            # print(f"   - Base URL: {base_url}")
            
            # Show first function tool if available
            # if function_tools and len(function_tools) > 0:
            #     print(f"   - First function tool: {function_tools[0]}")
            print(f"   - Function tools: {function_tools}")
            print(f"   - System message: {system_message}")
            print(f"   - Base URL: {base_url}")
                
    except Exception as e:
        print(f"❌ Error for client_id '{client_id}': {e}")
        import traceback
        traceback.print_exc()
    
    # Close the pool
    await pool.close()
    print("\n=== Test completed ===")

# async def test_direct_database_query():
#     """Test direct database query to see raw data."""
    
#     print("\n=== Testing direct database query ===\n")
    
#     try:
#         pool = await asyncpg.create_pool(
#             user=os.getenv('user'),
#             password=os.getenv('password'),
#             database=os.getenv('dbname'),
#             host=os.getenv('host'),
#             port=os.getenv('port'),
#             min_size=1,
#             max_size=2
#         )
        
#         async with pool.acquire() as conn:
#             # Check if table exists
#             table_exists = await conn.fetchval("""
#                 SELECT EXISTS (
#                     SELECT FROM information_schema.tables 
#                     WHERE table_schema = 'chatbot' 
#                     AND table_name = 'function_definitions'
#                 );
#             """)
            
#             print(f"Table 'chatbot.function_definitions' exists: {table_exists}")
            
#             if table_exists:
#                 # Get table structure
#                 columns = await conn.fetch("""
#                     SELECT column_name, data_type 
#                     FROM information_schema.columns 
#                     WHERE table_schema = 'chatbot' 
#                     AND table_name = 'function_definitions'
#                     ORDER BY ordinal_position;
#                 """)
                
#                 print("\nTable structure:")
#                 for col in columns:
#                     print(f"  - {col['column_name']}: {col['data_type']}")
                
#                 # Get all records
#                 records = await conn.fetch("""
#                     SELECT organisation_id, 
#                            pg_typeof(function_schema) as function_schema_type,
#                            length(function_schema::text) as function_schema_length,
#                            system_prompt,
#                            api_link
#                     FROM chatbot.function_definitions;
#                 """)
                
#                 print(f"\nFound {len(records)} records:")
#                 for record in records:
#                     print(f"  - Org ID: {record['organisation_id']}")
#                     print(f"    Function schema type: {record['function_schema_type']}")
#                     print(f"    Function schema length: {record['function_schema_length']}")
#                     print(f"    System prompt: {record['system_prompt'][:50]}..." if record['system_prompt'] else "None")
#                     print(f"    API link: {record['api_link']}")
#                     print()
                
#                 # Get the actual function_schema for TCS
#                 tcs_record = await conn.fetchrow("""
#                     SELECT function_schema, system_prompt, api_link
#                     FROM chatbot.function_definitions
#                     WHERE organisation_id = $1
#                 """, "TCS")
                
#                 if tcs_record:
#                     print("Raw TCS record:")
#                     print(f"  - function_schema type: {type(tcs_record['function_schema'])}")
#                     print(f"  - function_schema value: {tcs_record['function_schema']}")
#                     print(f"  - system_prompt: {tcs_record['system_prompt'][:100]}..." if tcs_record['system_prompt'] else "None")
#                     print(f"  - api_link: {tcs_record['api_link']}")
            
#         await pool.close()
        
#     except Exception as e:
#         print(f"❌ Error in direct database query: {e}")
#         import traceback
#         traceback.print_exc()

if __name__ == "__main__":
    print("Starting database function tests...\n")
    
    # Run the tests
    # asyncio.run(test_direct_database_query())
    asyncio.run(test_read_function_schema())
